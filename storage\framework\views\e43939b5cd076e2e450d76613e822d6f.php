<?php $__env->startSection('title', 'Beranda'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gray-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Serasi Style
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Temukan koleksi pakaian terbaik dengan gaya yang serasi untuk setiap momen
            </p>
            <a href="<?php echo e(route('products.index')); ?>" class="bg-gray-900 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-800 transition duration-300">
                Belanja <PERSON>
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Kategori Produk</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('categories.show', $category)); ?>"
                   class="group text-center">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden group-hover:shadow-md transition duration-300">
                        <div class="aspect-w-4 aspect-h-3 bg-gray-200">
                            <?php if($category->image): ?>
                                <img src="<?php echo e($category->image); ?>" alt="<?php echo e($category->name); ?>" class="w-full h-32 object-cover group-hover:scale-105 transition duration-300">
                            <?php else: ?>
                                <div class="w-full h-32 bg-gray-300 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-1"><?php echo e($category->name); ?></h3>
                            <p class="text-sm text-gray-600"><?php echo e($category->activeProducts->count()); ?> produk</p>
                        </div>
                    </div>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Produk Unggulan</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php $__currentLoopData = $featuredProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                    <a href="<?php echo e(route('products.show', $product)); ?>">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200 relative overflow-hidden">
                            <?php if($product->main_image): ?>
                                <img src="<?php echo e($product->main_image); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            <?php elseif($product->images && count($product->images) > 0): ?>
                                <img src="<?php echo e($product->images[0]); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            <?php else: ?>
                                <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            <?php if($product->is_on_sale): ?>
                                <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                                    DISKON
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2"><?php echo e($product->name); ?></h3>
                            <p class="text-sm text-gray-600 mb-2"><?php echo e($product->category->name); ?></p>
                            <div class="flex items-center justify-between">
                                <div>
                                    <?php if($product->is_on_sale): ?>
                                        <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->sale_price, 0, ',', '.')); ?></span>
                                        <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                    <?php else: ?>
                                        <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($product->rating > 0): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                        </svg>
                                        <span class="text-sm text-gray-600 ml-1"><?php echo e($product->rating); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="text-center mt-8">
            <a href="<?php echo e(route('products.index')); ?>" class="bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-300">
                Lihat Semua Produk
            </a>
        </div>
    </div>
</section>

<!-- New Products -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Produk Terbaru</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php $__currentLoopData = $newProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                    <a href="<?php echo e(route('products.show', $product)); ?>">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200 relative overflow-hidden">
                            <?php if($product->main_image): ?>
                                <img src="<?php echo e($product->main_image); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            <?php elseif($product->images && count($product->images) > 0): ?>
                                <img src="<?php echo e($product->images[0]); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            <?php else: ?>
                                <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            <?php if($product->is_on_sale): ?>
                                <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                                    SALE
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2"><?php echo e($product->name); ?></h3>
                            <p class="text-sm text-gray-600 mb-2"><?php echo e($product->category->name); ?></p>
                            <div class="flex items-center justify-between">
                                <div>
                                    <?php if($product->is_on_sale): ?>
                                        <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->sale_price, 0, ',', '.')); ?></span>
                                        <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                    <?php else: ?>
                                        <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($product->rating > 0): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                        </svg>
                                        <span class="text-sm text-gray-600 ml-1"><?php echo e($product->rating); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Herd\e-commerce-mini-skincare\resources\views/home.blade.php ENDPATH**/ ?>