@extends('layouts.app')

@section('title', 'Wishlist')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Wishlist Saya</h1>

    @if($wishlistItems->count() > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach($wishlistItems as $item)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                    <div class="relative">
                        <a href="{{ route('products.show', $item->product) }}">
                            <div class="aspect-w-1 aspect-h-1 bg-gray-200">
                                @if($item->product->images && count($item->product->images) > 0)
                                    <img src="{{ $item->product->main_image }}" alt="{{ $item->product->name }}" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                                @else
                                    <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500">No Image</span>
                                    </div>
                                @endif
                            </div>
                        </a>
                        
                        <!-- Remove from Wishlist Button -->
                        <form action="{{ route('wishlist.remove', $item->product) }}" method="POST" class="absolute top-2 right-2" onsubmit="return confirm('Hapus dari wishlist?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition duration-300">
                                <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </form>

                        <!-- Sale Badge -->
                        @if($item->product->is_on_sale)
                            <div class="absolute top-2 left-2">
                                <span class="bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
                                    {{ round((($item->product->price - $item->product->sale_price) / $item->product->price) * 100) }}% OFF
                                </span>
                            </div>
                        @endif
                    </div>

                    <div class="p-4">
                        <a href="{{ route('products.show', $item->product) }}">
                            <h3 class="font-semibold text-gray-900 mb-2 hover:text-gray-700">{{ $item->product->name }}</h3>
                        </a>
                        <p class="text-sm text-gray-600 mb-2">{{ $item->product->category->name }}</p>
                        
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                @if($item->product->is_on_sale)
                                    <span class="text-lg font-bold text-gray-900">Rp {{ number_format($item->product->sale_price, 0, ',', '.') }}</span>
                                    <span class="text-sm text-gray-500 line-through ml-2">Rp {{ number_format($item->product->price, 0, ',', '.') }}</span>
                                @else
                                    <span class="text-lg font-bold text-gray-900">Rp {{ number_format($item->product->price, 0, ',', '.') }}</span>
                                @endif
                            </div>
                            @if($item->product->rating > 0)
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                    </svg>
                                    <span class="text-sm text-gray-600 ml-1">{{ $item->product->rating }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Stock Status -->
                        @if($item->product->stock_quantity > 0)
                            <p class="text-sm text-green-600 mb-3">Stok tersedia</p>
                        @else
                            <p class="text-sm text-red-600 mb-3">Stok habis</p>
                        @endif

                        <!-- Add to Cart Button -->
                        @if($item->product->stock_quantity > 0)
                            <form action="{{ route('cart.add') }}" method="POST">
                                @csrf
                                <input type="hidden" name="product_id" value="{{ $item->product->id }}">
                                <input type="hidden" name="quantity" value="1">
                                <button type="submit" class="w-full bg-gray-900 text-white py-2 px-4 rounded-lg text-sm font-semibold hover:bg-gray-800 transition duration-300">
                                    Tambah ke Keranjang
                                </button>
                            </form>
                        @else
                            <button disabled class="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-lg text-sm font-semibold cursor-not-allowed">
                                Stok Habis
                            </button>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Summary -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Total {{ $wishlistItems->count() }} item dalam wishlist</h3>
                    <p class="text-gray-600 mt-1">Produk yang Anda simpan untuk dibeli nanti</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <a href="{{ route('products.index') }}" class="bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-300">
                        Lanjut Belanja
                    </a>
                </div>
            </div>
        </div>
    @else
        <!-- Empty Wishlist -->
        <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
                <svg class="w-24 h-24 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.682l-1.318-1.364a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Wishlist Anda Kosong</h3>
            <p class="text-gray-600 mb-6">Belum ada produk yang ditambahkan ke wishlist</p>
            <a href="{{ route('products.index') }}" class="bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-300">
                Mulai Belanja
            </a>
        </div>
    @endif
</div>
@endsection
