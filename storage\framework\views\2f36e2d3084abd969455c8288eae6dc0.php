<?php $__env->startSection('title', $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?php echo e(route('home')); ?>" class="text-gray-700 hover:text-gray-900">Beranda</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="<?php echo e(route('products.index')); ?>" class="ml-1 text-gray-700 hover:text-gray-900">Produk</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="<?php echo e(route('products.index', ['category' => $product->category->slug])); ?>" class="ml-1 text-gray-700 hover:text-gray-900"><?php echo e($product->category->name); ?></a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500"><?php echo e($product->name); ?></span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Product Images -->
        <div class="space-y-4">
            <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                <?php if($product->images && count($product->images) > 0): ?>
                    <img id="main-image" src="<?php echo e($product->main_image); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-96 object-cover">
                <?php else: ?>
                    <div class="w-full h-96 bg-gray-300 flex items-center justify-center">
                        <span class="text-gray-500">No Image</span>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if($product->images && count($product->images) > 1): ?>
                <div class="grid grid-cols-4 gap-2">
                    <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <button onclick="changeMainImage('<?php echo e($image); ?>')" class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden hover:opacity-75">
                            <img src="<?php echo e($image); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-20 object-cover">
                        </button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Product Info -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?php echo e($product->name); ?></h1>
                <p class="text-lg text-gray-600 mt-2"><?php echo e($product->category->name); ?></p>
            </div>

            <!-- Rating -->
            <?php if($product->rating > 0): ?>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <svg class="w-5 h-5 <?php echo e($i <= $product->rating ? 'text-yellow-400' : 'text-gray-300'); ?> fill-current" viewBox="0 0 20 20">
                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                            </svg>
                        <?php endfor; ?>
                    </div>
                    <span class="text-sm text-gray-600">(<?php echo e($product->reviews->count()); ?> ulasan)</span>
                </div>
            <?php endif; ?>

            <!-- Price -->
            <div class="space-y-2">
                <?php if($product->is_on_sale): ?>
                    <div class="flex items-center space-x-3">
                        <span class="text-3xl font-bold text-gray-900">Rp <?php echo e(number_format($product->sale_price, 0, ',', '.')); ?></span>
                        <span class="text-xl text-gray-500 line-through">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                        <span class="bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded">
                            <?php echo e(round((($product->price - $product->sale_price) / $product->price) * 100)); ?>% OFF
                        </span>
                    </div>
                <?php else: ?>
                    <span class="text-3xl font-bold text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                <?php endif; ?>
            </div>

            <!-- Short Description -->
            <p class="text-gray-700"><?php echo e($product->short_description); ?></p>

            <!-- Add to Cart Form -->
            <form action="<?php echo e(route('cart.add')); ?>" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="product_id" value="<?php echo e($product->id); ?>">
                
                <!-- Variants -->
                <?php if($product->variants->count() > 0): ?>
                    <!-- Size Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ukuran</label>
                        <div class="grid grid-cols-4 gap-2">
                            <?php $__currentLoopData = $product->variants->groupBy('size'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size => $variants): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="relative">
                                    <input type="radio" name="size" value="<?php echo e($size); ?>" class="sr-only peer" required>
                                    <div class="border border-gray-300 rounded-lg p-3 text-center cursor-pointer peer-checked:border-gray-900 peer-checked:bg-gray-900 peer-checked:text-white hover:border-gray-400">
                                        <?php echo e($size); ?>

                                    </div>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Color Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Warna</label>
                        <div class="flex space-x-2">
                            <?php $__currentLoopData = $product->variants->groupBy('color'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color => $variants): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="relative">
                                    <input type="radio" name="color" value="<?php echo e($color); ?>" class="sr-only peer" required>
                                    <div class="w-8 h-8 rounded-full border-2 border-gray-300 cursor-pointer peer-checked:border-gray-900 peer-checked:ring-2 peer-checked:ring-gray-900 peer-checked:ring-offset-2" 
                                         style="background-color: <?php echo e($variants->first()->color_code); ?>" 
                                         title="<?php echo e($color); ?>">
                                    </div>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Quantity -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah</label>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="decrementQuantity()" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                            </svg>
                        </button>
                        <input type="number" name="quantity" id="quantity" value="1" min="1" max="<?php echo e($product->stock_quantity); ?>" class="w-20 text-center border border-gray-300 rounded-md py-2">
                        <button type="button" onclick="incrementQuantity()" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Stok tersedia: <?php echo e($product->stock_quantity); ?></p>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <button type="submit" class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition duration-300">
                        Tambah ke Keranjang
                    </button>
                    
                    <?php if(auth()->guard()->check()): ?>
                        <button type="button" onclick="toggleWishlist(<?php echo e($product->id); ?>)" class="w-full border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.682l-1.318-1.364a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            Tambah ke Wishlist
                        </button>
                    <?php endif; ?>
                </div>
            </form>

            <!-- Product Attributes -->
            <?php if($product->attributes): ?>
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Detail Produk</h3>
                    <dl class="space-y-2">
                        <?php $__currentLoopData = $product->attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex">
                                <dt class="w-1/3 text-sm font-medium text-gray-700 capitalize"><?php echo e(str_replace('_', ' ', $key)); ?>:</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($value); ?></dd>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </dl>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Product Description -->
    <div class="mt-12 border-t border-gray-200 pt-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Deskripsi Produk</h2>
        <div class="prose max-w-none text-gray-700">
            <?php echo nl2br(e($product->description)); ?>

        </div>
    </div>

    <!-- Related Products -->
    <?php if($relatedProducts->count() > 0): ?>
        <div class="mt-12 border-t border-gray-200 pt-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Produk Terkait</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                        <a href="<?php echo e(route('products.show', $relatedProduct)); ?>">
                            <div class="aspect-w-1 aspect-h-1 bg-gray-200">
                                <?php if($relatedProduct->images && count($relatedProduct->images) > 0): ?>
                                    <img src="<?php echo e($relatedProduct->main_image); ?>" alt="<?php echo e($relatedProduct->name); ?>" class="w-full h-48 object-cover group-hover:scale-105 transition duration-300">
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500">No Image</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2"><?php echo e($relatedProduct->name); ?></h3>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <?php if($relatedProduct->is_on_sale): ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($relatedProduct->sale_price, 0, ',', '.')); ?></span>
                                            <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($relatedProduct->price, 0, ',', '.')); ?></span>
                                        <?php else: ?>
                                            <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($relatedProduct->price, 0, ',', '.')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function changeMainImage(src) {
    document.getElementById('main-image').src = src;
}

function incrementQuantity() {
    const input = document.getElementById('quantity');
    const max = parseInt(input.getAttribute('max'));
    if (parseInt(input.value) < max) {
        input.value = parseInt(input.value) + 1;
    }
}

function decrementQuantity() {
    const input = document.getElementById('quantity');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

function toggleWishlist(productId) {
    fetch(`/wishlist/${productId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button text or show notification
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Herd\e-commerce-mini-skincare\resources\views/products/show.blade.php ENDPATH**/ ?>