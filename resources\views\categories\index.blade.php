@extends('layouts.app')

@section('title', 'Kategori Produk')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Kategori Produk</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Je<PERSON>jahi koleksi lengkap kami berdasarkan kategori untuk menemukan produk yang sesuai dengan gaya Anda
        </p>
    </div>

    <!-- Categories Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @foreach($categories as $category)
            <a href="{{ route('categories.show', $category) }}" class="group">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group-hover:shadow-lg transition duration-300">
                    <!-- Category Image -->
                    <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                        @if($category->image)
                            <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-full h-48 object-cover group-hover:scale-105 transition duration-300">
                        @else
                            <div class="w-full h-48 bg-gray-300 flex items-center justify-center">
                                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                        @endif
                    </div>

                    <!-- Category Info -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-gray-700 transition duration-300">
                            {{ $category->name }}
                        </h3>
                        <p class="text-gray-600 mb-4">{{ $category->description }}</p>
                        
                        <!-- Stats -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-gray-900">{{ $category->active_products_count }}</div>
                                    <div class="text-xs text-gray-600">Produk</div>
                                </div>
                            </div>
                            <div class="flex items-center text-gray-400 group-hover:text-gray-600 transition duration-300">
                                <span class="text-sm mr-1">Lihat</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        @endforeach
    </div>

    <!-- Quick Links -->
    <div class="mt-16 text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Atau Jelajahi Semua Produk</h2>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('products.index') }}" class="bg-gray-900 text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition duration-300">
                Lihat Semua Produk
            </a>
            <a href="{{ route('products.index', ['featured' => 1]) }}" class="border border-gray-900 text-gray-900 px-8 py-3 rounded-lg hover:bg-gray-900 hover:text-white transition duration-300">
                Produk Unggulan
            </a>
        </div>
    </div>

    <!-- Featured Categories Section -->
    @if($categories->where('activeProducts.is_featured', true)->count() > 0)
        <div class="mt-20">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Kategori Unggulan</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($categories->take(4) as $category)
                    <div class="text-center">
                        <a href="{{ route('categories.show', $category) }}" class="group">
                            <div class="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden bg-gray-200 group-hover:shadow-lg transition duration-300">
                                @if($category->image)
                                    <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-full h-full object-cover group-hover:scale-110 transition duration-300">
                                @else
                                    <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-gray-700 transition duration-300">
                                {{ $category->name }}
                            </h3>
                            <p class="text-sm text-gray-600 mt-1">{{ $category->active_products_count }} produk</p>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
@endsection
