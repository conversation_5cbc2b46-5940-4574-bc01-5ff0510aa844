// Wishlist Management Class
class WishlistManager {
    constructor() {
        this.apiUrl = '/api/wishlist';
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add to wishlist buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-add-to-wishlist]') || e.target.closest('[data-add-to-wishlist]')) {
                e.preventDefault();
                const button = e.target.matches('[data-add-to-wishlist]') ? e.target : e.target.closest('[data-add-to-wishlist]');
                this.toggleWishlist(button);
            }
        });
    }

    async toggleWishlist(button) {
        const productId = button.dataset.productId;
        const isInWishlist = button.classList.contains('in-wishlist');

        // Check if user is authenticated
        if (!this.isAuthenticated()) {
            if (window.app) {
                window.app.showNotification('Silakan login terlebih dahulu untuk menggunakan wishlist', 'warning');
            }
            window.location.href = '/login';
            return;
        }

        try {
            // Update UI immediately for better UX
            this.updateWishlistButton(button, !isInWishlist);

            const response = await fetch(`${this.apiUrl}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateWishlistButton(button, data.data.in_wishlist);
                this.updateWishlistCount(data.data.wishlist_count);
                
                const message = data.data.in_wishlist ? 
                    'Produk ditambahkan ke wishlist' : 
                    'Produk dihapus dari wishlist';
                    
                if (window.app) {
                    window.app.showNotification(message, 'success');
                }
            } else {
                // Revert UI changes if API call failed
                this.updateWishlistButton(button, isInWishlist);
                if (window.app) {
                    window.app.showNotification(data.message || 'Terjadi kesalahan', 'error');
                }
            }
        } catch (error) {
            // Revert UI changes if request failed
            this.updateWishlistButton(button, isInWishlist);
            console.error('Wishlist error:', error);
            if (window.app) {
                window.app.showNotification('Terjadi kesalahan saat memperbarui wishlist', 'error');
            }
        }
    }

    updateWishlistButton(button, isInWishlist) {
        const heartIcon = button.querySelector('svg');
        
        if (isInWishlist) {
            button.classList.add('in-wishlist');
            button.classList.remove('text-gray-600');
            button.classList.add('text-red-500');
            if (heartIcon) {
                heartIcon.setAttribute('fill', 'currentColor');
            }
        } else {
            button.classList.remove('in-wishlist');
            button.classList.remove('text-red-500');
            button.classList.add('text-gray-600');
            if (heartIcon) {
                heartIcon.setAttribute('fill', 'none');
            }
        }
    }

    updateWishlistCount(count) {
        const wishlistCount = document.querySelector('#wishlist-count');
        if (wishlistCount) {
            wishlistCount.textContent = count;
            wishlistCount.style.display = count > 0 ? 'inline' : 'none';
        }
    }

    isAuthenticated() {
        // Check if user is authenticated
        return document.querySelector('meta[name="user-authenticated"]')?.content === 'true' ||
               document.querySelector('#user-menu') !== null;
    }

    async initializeProductWishlistState(productId) {
        if (!this.isAuthenticated()) return;

        try {
            const response = await fetch(`${this.apiUrl}/check/${productId}`);
            const data = await response.json();

            if (data.success) {
                const wishlistButtons = document.querySelectorAll(`[data-product-id="${productId}"][data-add-to-wishlist]`);
                wishlistButtons.forEach(button => {
                    this.updateWishlistButton(button, data.data.in_wishlist);
                });
            }
        } catch (error) {
            console.error('Error checking wishlist state:', error);
        }
    }
}

// Export for use in main app
window.WishlistManager = WishlistManager;
