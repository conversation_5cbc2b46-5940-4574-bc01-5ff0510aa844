<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'size',
        'color',
        'color_code',
        'price_adjustment',
        'stock_quantity',
        'sku',
        'is_active',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'stock_quantity' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the product that owns the variant.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the final price for this variant.
     */
    public function getFinalPriceAttribute()
    {
        $basePrice = $this->product->sale_price ?? $this->product->price;
        return $basePrice + $this->price_adjustment;
    }

    /**
     * Get the variant display name.
     */
    public function getDisplayNameAttribute()
    {
        $parts = [];
        if ($this->size) {
            $parts[] = "Size: {$this->size}";
        }
        if ($this->color) {
            $parts[] = "Color: {$this->color}";
        }
        return implode(', ', $parts);
    }

    /**
     * Scope a query to only include active variants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include in-stock variants.
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }
}
