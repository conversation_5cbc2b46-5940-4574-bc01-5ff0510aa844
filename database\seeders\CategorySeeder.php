<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'T-Shirt',
                'slug' => 't-shirt',
                'description' => 'Koleksi t-shirt casual dan formal untuk pria dan wanita',
                'sort_order' => 1,
            ],
            [
                'name' => 'Kemeja Wanita',
                'slug' => 'kemeja-wanita',
                'description' => 'Kemeja elegan dan stylish untuk wanita',
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'slug' => 'celana-wanita',
                'description' => 'Celana trendy dan nyaman untuk wanita',
                'sort_order' => 3,
            ],
            [
                'name' => 'Kemeja Pria',
                'slug' => 'kemeja-pria',
                'description' => 'Kemeja formal dan casual untuk pria',
                'sort_order' => 4,
            ],
            [
                'name' => 'Celana Pria',
                'slug' => 'celana-pria',
                'description' => 'Celana berkualitas untuk pria',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
