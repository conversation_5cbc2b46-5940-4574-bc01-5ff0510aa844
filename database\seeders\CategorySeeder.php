<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'T-Shirt',
                'slug' => 't-shirt',
                'description' => 'Koleksi t-shirt casual dan formal untuk pria dan wanita',
                'image' => 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop&crop=center',
                'sort_order' => 1,
            ],

            [
                'name' => '<PERSON><PERSON>',
                'slug' => 'celana-wanita',
                'description' => 'Celana trendy dan nyaman untuk wanita',
                'image' => 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400&h=300&fit=crop&crop=center',
                'sort_order' => 2,
            ],
            [
                'name' => 'Kemeja Pria',
                'slug' => 'kemeja-pria',
                'description' => 'Kemeja formal dan casual untuk pria',
                'image' => 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=300&fit=crop&crop=center',
                'sort_order' => 3,
            ],
            [
                'name' => 'Celana Pria',
                'slug' => 'celana-pria',
                'description' => 'Celana berkualitas untuk pria',
                'image' => 'https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=400&h=300&fit=crop&crop=center',
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
