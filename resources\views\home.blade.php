@extends('layouts.app')

@section('title', 'Beranda')

@section('content')
<!-- Hero Section -->
<section class="bg-gray-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Serasi Style
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Temukan koleksi pakaian terbaik dengan gaya yang serasi untuk setiap momen
            </p>
            <a href="{{ route('products.index') }}" class="bg-gray-900 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-800 transition duration-300">
                Belanja <PERSON>
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Kategori Produk</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            @foreach($categories as $category)
                <a href="{{ route('products.index', ['category' => $category->slug]) }}" 
                   class="group text-center">
                    <div class="bg-gray-100 rounded-lg p-8 mb-4 group-hover:bg-gray-200 transition duration-300">
                        <div class="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4"></div>
                        <h3 class="font-semibold text-gray-900">{{ $category->name }}</h3>
                    </div>
                </a>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Produk Unggulan</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($featuredProducts as $product)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                    <a href="{{ route('products.show', $product) }}">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200">
                            @if($product->images && count($product->images) > 0)
                                <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            @else
                                <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                    <span class="text-gray-500">No Image</span>
                                </div>
                            @endif
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">{{ $product->name }}</h3>
                            <p class="text-sm text-gray-600 mb-2">{{ $product->category->name }}</p>
                            <div class="flex items-center justify-between">
                                <div>
                                    @if($product->is_on_sale)
                                        <span class="text-lg font-bold text-gray-900">Rp {{ number_format($product->sale_price, 0, ',', '.') }}</span>
                                        <span class="text-sm text-gray-500 line-through ml-2">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                    @else
                                        <span class="text-lg font-bold text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                    @endif
                                </div>
                                @if($product->rating > 0)
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                        </svg>
                                        <span class="text-sm text-gray-600 ml-1">{{ $product->rating }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
        <div class="text-center mt-8">
            <a href="{{ route('products.index') }}" class="bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition duration-300">
                Lihat Semua Produk
            </a>
        </div>
    </div>
</section>

<!-- New Products -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Produk Terbaru</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($newProducts as $product)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                    <a href="{{ route('products.show', $product) }}">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200">
                            @if($product->images && count($product->images) > 0)
                                <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                            @else
                                <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                    <span class="text-gray-500">No Image</span>
                                </div>
                            @endif
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">{{ $product->name }}</h3>
                            <p class="text-sm text-gray-600 mb-2">{{ $product->category->name }}</p>
                            <div class="flex items-center justify-between">
                                <div>
                                    @if($product->is_on_sale)
                                        <span class="text-lg font-bold text-gray-900">Rp {{ number_format($product->sale_price, 0, ',', '.') }}</span>
                                        <span class="text-sm text-gray-500 line-through ml-2">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                    @else
                                        <span class="text-lg font-bold text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                    @endif
                                </div>
                                @if($product->rating > 0)
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                        </svg>
                                        <span class="text-sm text-gray-600 ml-1">{{ $product->rating }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endsection
