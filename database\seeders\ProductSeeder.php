<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();

        $products = [
            // T-Shirt
            [
                'name' => 'Kaos Katun Basic',
                'description' => 'T-shirt katun berkualitas tinggi dengan desain minimalis yang cocok untuk aktivitas sehari-hari.',
                'short_description' => 'T-shirt katun basic dengan kualitas premium',
                'price' => 89000,
                'sale_price' => 75000,
                'sku' => 'TS-001',
                'stock_quantity' => 50,
                'category_id' => $categories->where('slug', 't-shirt')->first()->id,
                'is_featured' => true,
                'main_image' => 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Katun 100%', 'care' => 'Cuci dengan mesin'],
            ],
            [
                'name' => 'Polo Shirt Premium',
                'description' => 'Polo shirt premium dengan bahan pique cotton yang nyaman dan breathable.',
                'short_description' => 'Polo shirt premium dengan bahan berkualitas',
                'price' => 125000,
                'sku' => 'TS-002',
                'stock_quantity' => 30,
                'category_id' => $categories->where('slug', 't-shirt')->first()->id,
                'main_image' => 'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1622445275576-721325763afe?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Katun Pique', 'collar' => 'Kerah polo'],
            ],
            
            // Kemeja Wanita
            [
                'name' => 'Blouse Putih Elegan',
                'description' => 'Blouse putih elegan dengan detail button yang cocok untuk acara formal maupun casual.',
                'short_description' => 'Blouse putih elegan untuk berbagai acara',
                'price' => 150000,
                'sale_price' => 120000,
                'sku' => 'KW-001',
                'stock_quantity' => 25,
                'category_id' => $categories->where('slug', 'kemeja-wanita')->first()->id,
                'is_featured' => true,
                'main_image' => 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Campuran Polyester', 'sleeve' => 'Lengan panjang'],
            ],
            [
                'name' => 'Kemeja Bergaris Kasual',
                'description' => 'Kemeja bergaris casual yang trendy dan nyaman untuk aktivitas sehari-hari.',
                'short_description' => 'Kemeja bergaris casual dan trendy',
                'price' => 110000,
                'sku' => 'KW-002',
                'stock_quantity' => 40,
                'category_id' => $categories->where('slug', 'kemeja-wanita')->first()->id,
                'main_image' => 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['pattern' => 'Bergaris', 'fit' => 'Potongan reguler'],
            ],
            
            // Celana Wanita
            [
                'name' => 'Jeans Pinggang Tinggi',
                'description' => 'Jeans high waist dengan potongan yang flattering dan bahan denim berkualitas.',
                'short_description' => 'Jeans high waist dengan potongan flattering',
                'price' => 200000,
                'sku' => 'CW-001',
                'stock_quantity' => 35,
                'category_id' => $categories->where('slug', 'celana-wanita')->first()->id,
                'is_featured' => true,
                'main_image' => 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1582418702059-97ebafb35d09?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Denim', 'fit' => 'Pinggang tinggi'],
            ],
            [
                'name' => 'Celana Formal Wanita',
                'description' => 'Celana formal dengan bahan berkualitas tinggi yang cocok untuk acara resmi.',
                'short_description' => 'Celana formal berkualitas tinggi',
                'price' => 175000,
                'sku' => 'CW-002',
                'stock_quantity' => 20,
                'category_id' => $categories->where('slug', 'celana-wanita')->first()->id,
                'main_image' => 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Polyester blend', 'style' => 'Formal'],
            ],
            
            // Kemeja Pria
            [
                'name' => 'Kemeja Putih Klasik',
                'description' => 'Kemeja putih klasik yang wajib dimiliki setiap pria untuk berbagai acara formal.',
                'short_description' => 'Kemeja putih klasik untuk pria',
                'price' => 140000,
                'sku' => 'KP-001',
                'stock_quantity' => 45,
                'category_id' => $categories->where('slug', 'kemeja-pria')->first()->id,
                'is_featured' => true,
                'main_image' => 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Katun', 'collar' => 'Kerah spread'],
            ],
            [
                'name' => 'Kemeja Kotak Kasual',
                'description' => 'Kemeja kotak-kotak casual yang cocok untuk gaya santai namun tetap stylish.',
                'short_description' => 'Kemeja kotak-kotak casual stylish',
                'price' => 120000,
                'sale_price' => 95000,
                'sku' => 'KP-002',
                'stock_quantity' => 30,
                'category_id' => $categories->where('slug', 'kemeja-pria')->first()->id,
                'main_image' => 'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['pattern' => 'Kotak-kotak', 'style' => 'Kasual'],
            ],
            
            // Celana Pria
            [
                'name' => 'Chinos Slim Fit',
                'description' => 'Celana chinos slim fit yang versatile dan nyaman untuk berbagai aktivitas.',
                'short_description' => 'Celana chinos slim fit versatile',
                'price' => 180000,
                'sku' => 'CP-001',
                'stock_quantity' => 40,
                'category_id' => $categories->where('slug', 'celana-pria')->first()->id,
                'main_image' => 'https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Katun twill', 'fit' => 'Potongan slim'],
            ],
            [
                'name' => 'Celana Formal Pria',
                'description' => 'Celana formal dengan bahan premium yang cocok untuk acara bisnis dan formal.',
                'short_description' => 'Celana formal premium untuk bisnis',
                'price' => 220000,
                'sku' => 'CP-002',
                'stock_quantity' => 25,
                'category_id' => $categories->where('slug', 'celana-pria')->first()->id,
                'is_featured' => true,
                'main_image' => 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=500&h=600&fit=crop&crop=center',
                'images' => [
                    'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=600&fit=crop&crop=center',
                    'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop&crop=center'
                ],
                'attributes' => ['material' => 'Campuran wol', 'style' => 'Formal'],
            ],
        ];

        foreach ($products as $productData) {
            $productData['slug'] = Str::slug($productData['name']);
            $product = Product::create($productData);

            // Create variants for each product
            $sizes = ['S', 'M', 'L', 'XL'];
            $colors = [
                ['name' => 'Hitam', 'code' => '#000000'],
                ['name' => 'Putih', 'code' => '#FFFFFF'],
                ['name' => 'Abu-abu', 'code' => '#808080'],
            ];

            foreach ($sizes as $size) {
                foreach ($colors as $color) {
                    ProductVariant::create([
                        'product_id' => $product->id,
                        'size' => $size,
                        'color' => $color['name'],
                        'color_code' => $color['code'],
                        'price_adjustment' => 0,
                        'stock_quantity' => rand(5, 15),
                        'sku' => $product->sku . '-' . $size . '-' . strtoupper(substr($color['name'], 0, 1)),
                    ]);
                }
            }
        }
    }
}
