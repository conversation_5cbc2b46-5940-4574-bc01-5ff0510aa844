<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of the categories.
     */
    public function index()
    {
        $categories = Category::active()
            ->ordered()
            ->withCount('activeProducts')
            ->get();

        return view('categories.index', compact('categories'));
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        // Get products for this category (limit to 8 for preview)
        $products = $category->activeProducts()
            ->with('category')
            ->take(8)
            ->get();

        return view('categories.show', compact('category', 'products'));
    }
}
