// Search Management Class
class SearchManager {
    constructor() {
        this.apiUrl = '/api/products';
        this.searchTimeout = null;
        this.minSearchLength = 2;
        this.setupSearchAutocomplete();
    }

    setupSearchAutocomplete() {
        const searchInput = document.querySelector('#search-input');
        if (!searchInput) return;

        // Create autocomplete dropdown
        const dropdown = document.createElement('div');
        dropdown.id = 'search-dropdown';
        dropdown.className = 'absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-b-lg shadow-lg z-50 hidden';
        searchInput.parentNode.appendChild(dropdown);

        // Hide dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }

    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        
        if (query.length < this.minSearchLength) {
            this.hideDropdown();
            return;
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300); // Debounce search
    }

    async performSearch(query) {
        try {
            const response = await fetch(`${this.apiUrl}/suggestions?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data.data);
            }
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    displaySearchResults(results) {
        const dropdown = document.querySelector('#search-dropdown');
        if (!dropdown) return;

        if (results.length === 0) {
            dropdown.innerHTML = '<div class="p-4 text-gray-500 text-center">Tidak ada hasil ditemukan</div>';
        } else {
            dropdown.innerHTML = results.map(product => `
                <a href="/products/${product.id}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center space-x-3">
                        <img src="${product.main_image}" alt="${product.name}" class="w-10 h-10 object-cover rounded">
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">${product.name}</h4>
                            <p class="text-sm text-gray-500">${product.formatted_price}</p>
                        </div>
                    </div>
                </a>
            `).join('');
        }

        this.showDropdown();
    }

    showDropdown() {
        const dropdown = document.querySelector('#search-dropdown');
        if (dropdown) {
            dropdown.classList.remove('hidden');
        }
    }

    hideDropdown() {
        const dropdown = document.querySelector('#search-dropdown');
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }

    // Advanced search with filters
    async advancedSearch(filters) {
        const params = new URLSearchParams();
        
        Object.keys(filters).forEach(key => {
            if (filters[key] !== null && filters[key] !== '') {
                params.append(key, filters[key]);
            }
        });

        try {
            const response = await fetch(`${this.apiUrl}?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                this.updateProductGrid(data.data);
                this.updatePagination(data.pagination);
            }
        } catch (error) {
            console.error('Advanced search error:', error);
        }
    }

    updateProductGrid(products) {
        const productGrid = document.querySelector('#product-grid');
        if (!productGrid) return;

        if (products.length === 0) {
            productGrid.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">🔍</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada produk ditemukan</h3>
                    <p class="text-gray-500">Coba ubah filter pencarian Anda</p>
                </div>
            `;
            return;
        }

        productGrid.innerHTML = products.map(product => this.createProductCard(product)).join('');
    }

    createProductCard(product) {
        const salePrice = product.sale_price ? `
            <div class="flex items-center space-x-2">
                <span class="text-lg font-bold text-gray-900">${product.formatted_sale_price}</span>
                <span class="text-sm text-gray-500 line-through">${product.formatted_price}</span>
            </div>
        ` : `
            <span class="text-lg font-bold text-gray-900">${product.formatted_price}</span>
        `;

        const saleBadge = product.sale_price ? `
            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                Sale
            </div>
        ` : '';

        return `
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="relative">
                    ${saleBadge}
                    <a href="/products/${product.id}">
                        <img src="${product.main_image}" alt="${product.name}" class="w-full h-64 object-cover">
                    </a>
                    <button data-add-to-wishlist data-product-id="${product.id}" 
                            class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <div class="mb-2">
                        <span class="text-xs text-gray-500 uppercase tracking-wide">${product.category.name}</span>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        <a href="/products/${product.id}" class="hover:text-gray-700">${product.name}</a>
                    </h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">${product.short_description}</p>
                    <div class="flex items-center justify-between">
                        ${salePrice}
                        <button data-add-to-cart data-product-id="${product.id}" 
                                class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 transition-colors text-sm">
                            Tambah ke Keranjang
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    updatePagination(pagination) {
        const paginationContainer = document.querySelector('#pagination');
        if (!paginationContainer || !pagination) return;

        const { current_page, last_page, from, to, total } = pagination;
        
        let paginationHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Menampilkan <span class="font-medium">${from}</span> sampai <span class="font-medium">${to}</span> 
                    dari <span class="font-medium">${total}</span> hasil
                </div>
                <div class="flex space-x-1">
        `;

        // Previous button
        if (current_page > 1) {
            paginationHTML += `
                <button data-page="${current_page - 1}" class="pagination-btn px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Previous
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(last_page, current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === current_page;
            paginationHTML += `
                <button data-page="${i}" class="pagination-btn px-3 py-2 text-sm border rounded-md ${
                    isActive 
                        ? 'bg-black text-white border-black' 
                        : 'bg-white border-gray-300 hover:bg-gray-50'
                }">
                    ${i}
                </button>
            `;
        }

        // Next button
        if (current_page < last_page) {
            paginationHTML += `
                <button data-page="${current_page + 1}" class="pagination-btn px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Next
                </button>
            `;
        }

        paginationHTML += `
                </div>
            </div>
        `;

        paginationContainer.innerHTML = paginationHTML;

        // Add click handlers for pagination
        paginationContainer.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(btn.dataset.page);
                this.loadPage(page);
            });
        });
    }

    async loadPage(page) {
        const currentFilters = this.getCurrentFilters();
        currentFilters.page = page;
        
        await this.advancedSearch(currentFilters);
        
        // Scroll to top of product grid
        const productGrid = document.querySelector('#product-grid');
        if (productGrid) {
            productGrid.scrollIntoView({ behavior: 'smooth' });
        }
    }

    getCurrentFilters() {
        const filters = {};
        
        // Get search query
        const searchInput = document.querySelector('#search-input');
        if (searchInput && searchInput.value) {
            filters.search = searchInput.value;
        }

        // Get category filter
        const categoryFilter = document.querySelector('[data-filter="category"]:checked');
        if (categoryFilter) {
            filters.category = categoryFilter.value;
        }

        // Get price range
        const minPrice = document.querySelector('#min-price');
        const maxPrice = document.querySelector('#max-price');
        if (minPrice && minPrice.value) filters.min_price = minPrice.value;
        if (maxPrice && maxPrice.value) filters.max_price = maxPrice.value;

        // Get sort option
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect && sortSelect.value) {
            filters.sort_by = sortSelect.value;
        }

        return filters;
    }
}

// Export for use in main app
window.SearchManager = SearchManager;
