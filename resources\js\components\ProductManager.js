// Product Management Class
class ProductManager {
    constructor() {
        this.apiUrl = '/api/products';
        this.currentFilters = {};
        this.setupFilterHandlers();
        this.setupVariantSelectors();
    }

    setupFilterHandlers() {
        // Category filters
        const categoryFilters = document.querySelectorAll('[data-filter="category"]');
        categoryFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.applyFilters();
            });
        });

        // Price range filters
        const priceInputs = document.querySelectorAll('#min-price, #max-price');
        priceInputs.forEach(input => {
            input.addEventListener('input', this.debounce(() => {
                this.applyFilters();
            }, 500));
        });

        // Sort dropdown
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.applyFilters();
            });
        }

        // Clear filters button
        const clearFiltersBtn = document.querySelector('#clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }
    }

    setupVariantSelectors() {
        // Size selector
        const sizeOptions = document.querySelectorAll('[data-variant="size"]');
        sizeOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectVariant('size', option);
            });
        });

        // Color selector
        const colorOptions = document.querySelectorAll('[data-variant="color"]');
        colorOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectVariant('color', option);
            });
        });

        // Quantity controls
        const quantityControls = document.querySelectorAll('.quantity-control');
        quantityControls.forEach(control => {
            const decreaseBtn = control.querySelector('.quantity-decrease');
            const increaseBtn = control.querySelector('.quantity-increase');
            const input = control.querySelector('.quantity-input');

            if (decreaseBtn) {
                decreaseBtn.addEventListener('click', () => {
                    this.updateQuantity(input, -1);
                });
            }

            if (increaseBtn) {
                increaseBtn.addEventListener('click', () => {
                    this.updateQuantity(input, 1);
                });
            }

            if (input) {
                input.addEventListener('change', () => {
                    this.validateQuantity(input);
                });
            }
        });
    }

    selectVariant(type, element) {
        // Remove active class from siblings
        const siblings = element.parentNode.querySelectorAll(`[data-variant="${type}"]`);
        siblings.forEach(sibling => {
            sibling.classList.remove('active', 'ring-2', 'ring-black');
        });

        // Add active class to selected element
        element.classList.add('active', 'ring-2', 'ring-black');

        // Update hidden input if exists
        const hiddenInput = document.querySelector(`input[name="${type}"]`);
        if (hiddenInput) {
            hiddenInput.value = element.dataset.value;
        }

        // Update price if variant has different price
        this.updateVariantPrice(type, element.dataset.value);

        // Check stock availability
        this.checkVariantStock();
    }

    updateVariantPrice(type, value) {
        // This would typically fetch variant-specific pricing
        // For now, we'll just update the display
        const priceElement = document.querySelector('#product-price');
        const variantPrice = document.querySelector(`[data-variant="${type}"][data-value="${value}"]`).dataset.price;
        
        if (priceElement && variantPrice) {
            priceElement.textContent = variantPrice;
        }
    }

    checkVariantStock() {
        const selectedSize = document.querySelector('[data-variant="size"].active')?.dataset.value;
        const selectedColor = document.querySelector('[data-variant="color"].active')?.dataset.value;
        const productId = document.querySelector('[data-product-id]')?.dataset.productId;

        if (!productId) return;

        // Check stock for selected variant combination
        fetch(`/api/products/${productId}/variant-stock`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                size: selectedSize,
                color: selectedColor
            })
        })
        .then(response => response.json())
        .then(data => {
            this.updateStockDisplay(data.stock);
        })
        .catch(error => {
            console.error('Error checking variant stock:', error);
        });
    }

    updateStockDisplay(stock) {
        const stockElement = document.querySelector('#stock-status');
        const addToCartBtn = document.querySelector('[data-add-to-cart]');
        
        if (stockElement) {
            if (stock > 0) {
                stockElement.innerHTML = `<span class="text-green-600">✓ Stok tersedia (${stock})</span>`;
                if (addToCartBtn) {
                    addToCartBtn.disabled = false;
                    addToCartBtn.textContent = 'Tambah ke Keranjang';
                }
            } else {
                stockElement.innerHTML = '<span class="text-red-600">✗ Stok habis</span>';
                if (addToCartBtn) {
                    addToCartBtn.disabled = true;
                    addToCartBtn.textContent = 'Stok Habis';
                }
            }
        }
    }

    updateQuantity(input, change) {
        const currentValue = parseInt(input.value) || 1;
        const newValue = Math.max(1, currentValue + change);
        const maxStock = parseInt(input.dataset.maxStock) || 999;
        
        input.value = Math.min(newValue, maxStock);
        this.validateQuantity(input);
    }

    validateQuantity(input) {
        const value = parseInt(input.value);
        const min = parseInt(input.min) || 1;
        const max = parseInt(input.dataset.maxStock) || 999;

        if (isNaN(value) || value < min) {
            input.value = min;
        } else if (value > max) {
            input.value = max;
            app.showNotification(`Maksimal pembelian ${max} item`, 'warning');
        }
    }

    async applyFilters() {
        const filters = this.getCurrentFilters();
        
        // Show loading state
        this.showLoadingState();

        try {
            const params = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key] !== null && filters[key] !== '') {
                    params.append(key, filters[key]);
                }
            });

            const response = await fetch(`${this.apiUrl}?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                this.updateProductGrid(data.data);
                this.updatePagination(data.pagination);
                this.updateFilterCounts(data.filter_counts);
            }
        } catch (error) {
            console.error('Error applying filters:', error);
            app.showNotification('Terjadi kesalahan saat memfilter produk', 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    getCurrentFilters() {
        const filters = {};

        // Search query
        const searchInput = document.querySelector('#search-input');
        if (searchInput && searchInput.value.trim()) {
            filters.search = searchInput.value.trim();
        }

        // Category filter
        const selectedCategory = document.querySelector('[data-filter="category"]:checked');
        if (selectedCategory && selectedCategory.value !== 'all') {
            filters.category = selectedCategory.value;
        }

        // Price range
        const minPrice = document.querySelector('#min-price');
        const maxPrice = document.querySelector('#max-price');
        if (minPrice && minPrice.value) filters.min_price = minPrice.value;
        if (maxPrice && maxPrice.value) filters.max_price = maxPrice.value;

        // In stock filter
        const inStockFilter = document.querySelector('#in-stock-filter');
        if (inStockFilter && inStockFilter.checked) {
            filters.in_stock = true;
        }

        // Sort option
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect && sortSelect.value) {
            filters.sort_by = sortSelect.value;
        }

        return filters;
    }

    clearAllFilters() {
        // Clear search
        const searchInput = document.querySelector('#search-input');
        if (searchInput) searchInput.value = '';

        // Clear category filters
        const categoryFilters = document.querySelectorAll('[data-filter="category"]');
        categoryFilters.forEach(filter => {
            filter.checked = filter.value === 'all';
        });

        // Clear price range
        const minPrice = document.querySelector('#min-price');
        const maxPrice = document.querySelector('#max-price');
        if (minPrice) minPrice.value = '';
        if (maxPrice) maxPrice.value = '';

        // Clear in stock filter
        const inStockFilter = document.querySelector('#in-stock-filter');
        if (inStockFilter) inStockFilter.checked = false;

        // Reset sort
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect) sortSelect.value = 'newest';

        // Apply cleared filters
        this.applyFilters();
    }

    showLoadingState() {
        const productGrid = document.querySelector('#product-grid');
        if (productGrid) {
            productGrid.classList.add('opacity-50', 'pointer-events-none');
        }

        // Show loading spinner
        const loadingSpinner = document.querySelector('#loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.classList.remove('hidden');
        }
    }

    hideLoadingState() {
        const productGrid = document.querySelector('#product-grid');
        if (productGrid) {
            productGrid.classList.remove('opacity-50', 'pointer-events-none');
        }

        // Hide loading spinner
        const loadingSpinner = document.querySelector('#loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.classList.add('hidden');
        }
    }

    updateProductGrid(products) {
        const productGrid = document.querySelector('#product-grid');
        if (!productGrid) return;

        if (products.length === 0) {
            productGrid.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="text-gray-400 text-6xl mb-4">🔍</div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Tidak ada produk ditemukan</h3>
                    <p class="text-gray-500 mb-4">Coba ubah filter pencarian Anda</p>
                    <button id="clear-filters" class="bg-black text-white px-6 py-2 rounded hover:bg-gray-800 transition-colors">
                        Hapus Semua Filter
                    </button>
                </div>
            `;
            
            // Re-attach clear filters handler
            const clearBtn = productGrid.querySelector('#clear-filters');
            if (clearBtn) {
                clearBtn.addEventListener('click', () => this.clearAllFilters());
            }
            
            return;
        }

        productGrid.innerHTML = products.map(product => this.createProductCard(product)).join('');
    }

    createProductCard(product) {
        // This method would be similar to the one in SearchManager
        // Implementation details would be the same
        return window.SearchManager ? 
            new SearchManager().createProductCard(product) : 
            `<div class="product-card">Product: ${product.name}</div>`;
    }

    updatePagination(pagination) {
        // Similar to SearchManager pagination update
        if (window.SearchManager) {
            new SearchManager().updatePagination(pagination);
        }
    }

    updateFilterCounts(filterCounts) {
        // Update category filter counts
        if (filterCounts && filterCounts.categories) {
            Object.keys(filterCounts.categories).forEach(categorySlug => {
                const count = filterCounts.categories[categorySlug];
                const filterElement = document.querySelector(`[data-filter="category"][value="${categorySlug}"]`);
                if (filterElement) {
                    const label = filterElement.nextElementSibling;
                    if (label) {
                        const countSpan = label.querySelector('.filter-count') || document.createElement('span');
                        countSpan.className = 'filter-count text-gray-500 text-sm ml-1';
                        countSpan.textContent = `(${count})`;
                        if (!label.querySelector('.filter-count')) {
                            label.appendChild(countSpan);
                        }
                    }
                }
            });
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Export for use in main app
window.ProductManager = ProductManager;
