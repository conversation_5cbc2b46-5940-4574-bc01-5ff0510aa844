// Wishlist Management Class
class WishlistManager {
    constructor() {
        this.apiUrl = '/api/wishlist';
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add to wishlist buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-add-to-wishlist]') || e.target.closest('[data-add-to-wishlist]')) {
                e.preventDefault();
                const button = e.target.matches('[data-add-to-wishlist]') ? e.target : e.target.closest('[data-add-to-wishlist]');
                this.toggleWishlist(button);
            }

            if (e.target.matches('[data-remove-from-wishlist]') || e.target.closest('[data-remove-from-wishlist]')) {
                e.preventDefault();
                const button = e.target.matches('[data-remove-from-wishlist]') ? e.target : e.target.closest('[data-remove-from-wishlist]');
                this.removeFromWishlist(button.dataset.productId);
            }
        });
    }

    async toggleWishlist(button) {
        const productId = button.dataset.productId;
        const isInWishlist = button.classList.contains('in-wishlist');

        // Check if user is authenticated
        if (!this.isAuthenticated()) {
            app.showNotification('Silakan login terlebih dahulu untuk menggunakan wishlist', 'warning');
            // Redirect to login or show login modal
            window.location.href = '/login';
            return;
        }

        try {
            // Update UI immediately for better UX
            this.updateWishlistButton(button, !isInWishlist);

            const response = await fetch(`${this.apiUrl}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateWishlistButton(button, data.data.in_wishlist);
                this.updateWishlistCount(data.data.wishlist_count);
                
                const message = data.data.in_wishlist ? 
                    'Produk ditambahkan ke wishlist' : 
                    'Produk dihapus dari wishlist';
                app.showNotification(message, 'success');
            } else {
                // Revert UI changes if API call failed
                this.updateWishlistButton(button, isInWishlist);
                app.showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        } catch (error) {
            // Revert UI changes if request failed
            this.updateWishlistButton(button, isInWishlist);
            console.error('Wishlist error:', error);
            app.showNotification('Terjadi kesalahan saat memperbarui wishlist', 'error');
        }
    }

    async removeFromWishlist(productId) {
        try {
            const response = await fetch(`${this.apiUrl}/remove/${productId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                // Remove item from wishlist page if we're on it
                const wishlistItem = document.querySelector(`[data-wishlist-item="${productId}"]`);
                if (wishlistItem) {
                    wishlistItem.remove();
                }

                // Update all wishlist buttons for this product
                const wishlistButtons = document.querySelectorAll(`[data-product-id="${productId}"][data-add-to-wishlist]`);
                wishlistButtons.forEach(button => {
                    this.updateWishlistButton(button, false);
                });

                this.updateWishlistCount(data.data.wishlist_count);
                app.showNotification('Produk dihapus dari wishlist', 'success');

                // Check if wishlist is empty
                this.checkEmptyWishlist();
            } else {
                app.showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        } catch (error) {
            console.error('Remove from wishlist error:', error);
            app.showNotification('Terjadi kesalahan saat menghapus dari wishlist', 'error');
        }
    }

    async addToCartFromWishlist(productId) {
        try {
            const response = await fetch('/api/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                app.cart.updateCartUI(data.data);
                app.showNotification('Produk ditambahkan ke keranjang', 'success');
            } else {
                app.showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        } catch (error) {
            console.error('Add to cart from wishlist error:', error);
            app.showNotification('Terjadi kesalahan saat menambahkan ke keranjang', 'error');
        }
    }

    updateWishlistButton(button, isInWishlist) {
        const heartIcon = button.querySelector('svg');
        
        if (isInWishlist) {
            button.classList.add('in-wishlist');
            button.classList.remove('text-gray-600');
            button.classList.add('text-red-500');
            if (heartIcon) {
                heartIcon.setAttribute('fill', 'currentColor');
            }
            button.setAttribute('data-tooltip', 'Hapus dari wishlist');
        } else {
            button.classList.remove('in-wishlist');
            button.classList.remove('text-red-500');
            button.classList.add('text-gray-600');
            if (heartIcon) {
                heartIcon.setAttribute('fill', 'none');
            }
            button.setAttribute('data-tooltip', 'Tambah ke wishlist');
        }
    }

    updateWishlistCount(count) {
        const wishlistCount = document.querySelector('#wishlist-count');
        if (wishlistCount) {
            wishlistCount.textContent = count;
            wishlistCount.style.display = count > 0 ? 'inline' : 'none';
        }

        // Update wishlist page header if we're on it
        const wishlistHeader = document.querySelector('#wishlist-header-count');
        if (wishlistHeader) {
            wishlistHeader.textContent = `(${count})`;
        }
    }

    checkEmptyWishlist() {
        const wishlistGrid = document.querySelector('#wishlist-grid');
        if (!wishlistGrid) return;

        const wishlistItems = wishlistGrid.querySelectorAll('[data-wishlist-item]');
        
        if (wishlistItems.length === 0) {
            wishlistGrid.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="text-gray-400 text-6xl mb-4">💝</div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Wishlist Anda kosong</h3>
                    <p class="text-gray-500 mb-6">Mulai tambahkan produk favorit Anda ke wishlist</p>
                    <a href="/products" class="bg-black text-white px-6 py-3 rounded hover:bg-gray-800 transition-colors inline-block">
                        Jelajahi Produk
                    </a>
                </div>
            `;
        }
    }

    async loadWishlist() {
        if (!this.isAuthenticated()) {
            return;
        }

        try {
            const response = await fetch(this.apiUrl);
            const data = await response.json();

            if (data.success) {
                this.renderWishlistItems(data.data.items);
                this.updateWishlistCount(data.data.count);
            }
        } catch (error) {
            console.error('Load wishlist error:', error);
        }
    }

    renderWishlistItems(items) {
        const wishlistGrid = document.querySelector('#wishlist-grid');
        if (!wishlistGrid) return;

        if (items.length === 0) {
            this.checkEmptyWishlist();
            return;
        }

        wishlistGrid.innerHTML = items.map(item => this.createWishlistItemCard(item)).join('');
    }

    createWishlistItemCard(item) {
        const product = item.product;
        const salePrice = product.sale_price ? `
            <div class="flex items-center space-x-2">
                <span class="text-lg font-bold text-gray-900">${product.formatted_sale_price}</span>
                <span class="text-sm text-gray-500 line-through">${product.formatted_price}</span>
            </div>
        ` : `
            <span class="text-lg font-bold text-gray-900">${product.formatted_price}</span>
        `;

        const saleBadge = product.sale_price ? `
            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                Sale
            </div>
        ` : '';

        return `
            <div data-wishlist-item="${product.id}" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="relative">
                    ${saleBadge}
                    <a href="/products/${product.id}">
                        <img src="${product.main_image}" alt="${product.name}" class="w-full h-64 object-cover">
                    </a>
                    <button data-remove-from-wishlist data-product-id="${product.id}" 
                            class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors text-red-500"
                            data-tooltip="Hapus dari wishlist">
                        <svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <div class="mb-2">
                        <span class="text-xs text-gray-500 uppercase tracking-wide">${product.category.name}</span>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        <a href="/products/${product.id}" class="hover:text-gray-700">${product.name}</a>
                    </h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">${product.short_description}</p>
                    <div class="flex items-center justify-between">
                        ${salePrice}
                        <div class="flex space-x-2">
                            <button onclick="app.wishlist.addToCartFromWishlist(${product.id})" 
                                    class="bg-black text-white px-3 py-2 rounded hover:bg-gray-800 transition-colors text-sm">
                                Tambah ke Keranjang
                            </button>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-gray-500">
                        Ditambahkan ${this.formatDate(item.created_at)}
                    </div>
                </div>
            </div>
        `;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'kemarin';
        } else if (diffDays < 7) {
            return `${diffDays} hari lalu`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} minggu lalu`;
        } else {
            return date.toLocaleDateString('id-ID', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    }

    isAuthenticated() {
        // Check if user is authenticated
        // This could be done by checking for a specific element, cookie, or making an API call
        return document.querySelector('meta[name="user-authenticated"]')?.content === 'true' ||
               document.querySelector('#user-menu') !== null;
    }

    // Initialize wishlist state for product pages
    async initializeProductWishlistState(productId) {
        if (!this.isAuthenticated()) return;

        try {
            const response = await fetch(`${this.apiUrl}/check/${productId}`);
            const data = await response.json();

            if (data.success) {
                const wishlistButtons = document.querySelectorAll(`[data-product-id="${productId}"][data-add-to-wishlist]`);
                wishlistButtons.forEach(button => {
                    this.updateWishlistButton(button, data.data.in_wishlist);
                });
            }
        } catch (error) {
            console.error('Error checking wishlist state:', error);
        }
    }
}

// Export for use in main app
window.WishlistManager = WishlistManager;
