<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Cart;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CartApiController extends Controller
{
    /**
     * Get cart contents
     */
    public function index(): JsonResponse
    {
        $cartItems = $this->getCartItems();
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $cartItems,
                'summary' => $cartSummary
            ]
        ]);
    }

    /**
     * Add item to cart
     */
    public function add(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'size' => 'nullable|string',
            'color' => 'nullable|string'
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check stock
        if ($product->stock_quantity < $request->quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Stok tidak mencukupi'
            ], 400);
        }

        if (Auth::check()) {
            // For authenticated users, save to database
            $cartItem = Cart::where('user_id', Auth::id())
                ->where('product_id', $request->product_id)
                ->where('size', $request->size)
                ->where('color', $request->color)
                ->first();

            if ($cartItem) {
                $cartItem->quantity += $request->quantity;
                $cartItem->save();
            } else {
                Cart::create([
                    'user_id' => Auth::id(),
                    'product_id' => $request->product_id,
                    'quantity' => $request->quantity,
                    'size' => $request->size,
                    'color' => $request->color,
                    'price' => $product->current_price
                ]);
            }
        } else {
            // For guests, use session
            $cart = session()->get('cart', []);
            $cartKey = $request->product_id . '_' . $request->size . '_' . $request->color;

            if (isset($cart[$cartKey])) {
                $cart[$cartKey]['quantity'] += $request->quantity;
            } else {
                $cart[$cartKey] = [
                    'product_id' => $request->product_id,
                    'quantity' => $request->quantity,
                    'size' => $request->size,
                    'color' => $request->color,
                    'price' => $product->current_price
                ];
            }

            session()->put('cart', $cart);
        }

        $cartItems = $this->getCartItems();
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Produk berhasil ditambahkan ke keranjang',
            'data' => [
                'items' => $cartItems,
                'summary' => $cartSummary
            ]
        ]);
    }

    /**
     * Update cart item quantity
     */
    public function update(Request $request, $itemId): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1'
        ]);

        if (Auth::check()) {
            $cartItem = Cart::where('user_id', Auth::id())
                ->where('id', $itemId)
                ->firstOrFail();

            // Check stock
            if ($cartItem->product->stock_quantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stok tidak mencukupi'
                ], 400);
            }

            $cartItem->update(['quantity' => $request->quantity]);
        } else {
            $cart = session()->get('cart', []);
            if (isset($cart[$itemId])) {
                $product = Product::find($cart[$itemId]['product_id']);
                if ($product->stock_quantity < $request->quantity) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Stok tidak mencukupi'
                    ], 400);
                }
                $cart[$itemId]['quantity'] = $request->quantity;
                session()->put('cart', $cart);
            }
        }

        $cartItems = $this->getCartItems();
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Keranjang berhasil diperbarui',
            'data' => [
                'items' => $cartItems,
                'summary' => $cartSummary
            ]
        ]);
    }

    /**
     * Remove item from cart
     */
    public function remove($itemId): JsonResponse
    {
        if (Auth::check()) {
            Cart::where('user_id', Auth::id())
                ->where('id', $itemId)
                ->delete();
        } else {
            $cart = session()->get('cart', []);
            unset($cart[$itemId]);
            session()->put('cart', $cart);
        }

        $cartItems = $this->getCartItems();
        $cartSummary = $this->getCartSummary($cartItems);

        return response()->json([
            'success' => true,
            'message' => 'Produk berhasil dihapus dari keranjang',
            'data' => [
                'items' => $cartItems,
                'summary' => $cartSummary
            ]
        ]);
    }

    /**
     * Clear entire cart
     */
    public function clear(): JsonResponse
    {
        if (Auth::check()) {
            Cart::where('user_id', Auth::id())->delete();
        } else {
            session()->forget('cart');
        }

        return response()->json([
            'success' => true,
            'message' => 'Keranjang berhasil dikosongkan',
            'data' => [
                'items' => [],
                'summary' => [
                    'subtotal' => 0,
                    'total_items' => 0,
                    'total_quantity' => 0
                ]
            ]
        ]);
    }

    /**
     * Get cart item count
     */
    public function count(): JsonResponse
    {
        $cartItems = $this->getCartItems();
        $totalQuantity = $cartItems->sum('quantity');

        return response()->json([
            'success' => true,
            'data' => [
                'count' => $totalQuantity
            ]
        ]);
    }

    /**
     * Get cart items from database or session
     */
    private function getCartItems()
    {
        if (Auth::check()) {
            return Cart::with('product')
                ->where('user_id', Auth::id())
                ->get();
        } else {
            $cart = session()->get('cart', []);
            $cartItems = collect();

            foreach ($cart as $key => $item) {
                $product = Product::find($item['product_id']);
                if ($product) {
                    $cartItems->push((object)[
                        'id' => $key,
                        'product' => $product,
                        'quantity' => $item['quantity'],
                        'size' => $item['size'],
                        'color' => $item['color'],
                        'price' => $item['price']
                    ]);
                }
            }

            return $cartItems;
        }
    }

    /**
     * Calculate cart summary
     */
    private function getCartSummary($cartItems)
    {
        $subtotal = $cartItems->sum(function($item) {
            return $item->quantity * $item->price;
        });

        return [
            'subtotal' => $subtotal,
            'total_items' => $cartItems->count(),
            'total_quantity' => $cartItems->sum('quantity'),
            'formatted_subtotal' => 'Rp ' . number_format($subtotal, 0, ',', '.')
        ];
    }
}
