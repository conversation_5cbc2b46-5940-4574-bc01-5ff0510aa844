<?php $__env->startSection('title', 'Produk'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Categories Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Kategori Produk</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <a href="<?php echo e(route('products.index')); ?>"
               class="group text-center <?php echo e(!request('category') ? 'ring-2 ring-gray-900' : ''); ?>">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden group-hover:shadow-md transition duration-300">
                    <div class="aspect-w-4 aspect-h-3 bg-gray-200">
                        <div class="w-full h-24 bg-gray-300 flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-gray-900 text-sm">Semua</h3>
                        <p class="text-xs text-gray-600"><?php echo e($products->total()); ?> produk</p>
                    </div>
                </div>
            </a>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('products.index', ['category' => $category->slug])); ?>"
                   class="group text-center <?php echo e(request('category') === $category->slug ? 'ring-2 ring-gray-900' : ''); ?>">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden group-hover:shadow-md transition duration-300">
                        <div class="aspect-w-4 aspect-h-3 bg-gray-200">
                            <?php if($category->image): ?>
                                <img src="<?php echo e($category->image); ?>" alt="<?php echo e($category->name); ?>" class="w-full h-24 object-cover group-hover:scale-105 transition duration-300">
                            <?php else: ?>
                                <div class="w-full h-24 bg-gray-300 flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 text-sm"><?php echo e($category->name); ?></h3>
                            <p class="text-xs text-gray-600"><?php echo e($category->activeProducts->count()); ?> produk</p>
                        </div>
                    </div>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filter</h3>
                
                <form method="GET" action="<?php echo e(route('products.index')); ?>">
                    <!-- Keep search query -->
                    <?php if(request('search')): ?>
                        <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Kategori</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="category" value="" <?php echo e(!request('category') ? 'checked' : ''); ?> class="text-gray-900">
                                <span class="ml-2 text-sm text-gray-700">Semua</span>
                            </label>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="<?php echo e($category->slug); ?>" <?php echo e(request('category') === $category->slug ? 'checked' : ''); ?> class="text-gray-900">
                                    <span class="ml-2 text-sm text-gray-700"><?php echo e($category->name); ?></span>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Harga</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm text-gray-700 mb-1">Minimum</label>
                                <input type="number" name="min_price" value="<?php echo e(request('min_price')); ?>" placeholder="0" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-700 mb-1">Maximum</label>
                                <input type="number" name="max_price" value="<?php echo e(request('max_price')); ?>" placeholder="1000000" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-gray-900 text-white py-2 px-4 rounded-md hover:bg-gray-800 transition duration-300">
                        Terapkan Filter
                    </button>
                </form>
            </div>
        </div>

        <!-- Products -->
        <div class="lg:w-3/4">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Produk</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($products->total()); ?> produk ditemukan</p>
                </div>
                
                <!-- Sort -->
                <div class="mt-4 sm:mt-0">
                    <form method="GET" action="<?php echo e(route('products.index')); ?>" class="flex items-center">
                        <!-- Keep existing filters -->
                        <?php $__currentLoopData = request()->except(['sort', 'page']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        
                        <label class="text-sm text-gray-700 mr-2">Urutkan:</label>
                        <select name="sort" onchange="this.form.submit()" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option value="latest" <?php echo e(request('sort') === 'latest' ? 'selected' : ''); ?>>Terbaru</option>
                            <option value="price_low" <?php echo e(request('sort') === 'price_low' ? 'selected' : ''); ?>>Harga Terendah</option>
                            <option value="price_high" <?php echo e(request('sort') === 'price_high' ? 'selected' : ''); ?>>Harga Tertinggi</option>
                            <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Nama A-Z</option>
                            <option value="rating" <?php echo e(request('sort') === 'rating' ? 'selected' : ''); ?>>Rating Tertinggi</option>
                        </select>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <?php if($products->count() > 0): ?>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                            <a href="<?php echo e(route('products.show', $product)); ?>">
                                <div class="aspect-w-1 aspect-h-1 bg-gray-200 relative overflow-hidden">
                                    <?php if($product->main_image): ?>
                                        <img src="<?php echo e($product->main_image); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                                    <?php elseif($product->images && count($product->images) > 0): ?>
                                        <img src="<?php echo e($product->images[0]); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-64 object-cover group-hover:scale-105 transition duration-300">
                                    <?php else: ?>
                                        <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                                            <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($product->is_on_sale): ?>
                                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                                            SALE
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2"><?php echo e($product->name); ?></h3>
                                    <p class="text-sm text-gray-600 mb-2"><?php echo e($product->category->name); ?></p>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <?php if($product->is_on_sale): ?>
                                                <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->sale_price, 0, ',', '.')); ?></span>
                                                <span class="text-sm text-gray-500 line-through ml-2">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                            <?php else: ?>
                                                <span class="text-lg font-bold text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($product->rating > 0): ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                                <span class="text-sm text-gray-600 ml-1"><?php echo e($product->rating); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    <?php echo e($products->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada produk ditemukan</h3>
                    <p class="text-gray-600">Coba ubah filter atau kata kunci pencarian Anda.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Herd\e-commerce-mini-skincare\resources\views/products/index.blade.php ENDPATH**/ ?>