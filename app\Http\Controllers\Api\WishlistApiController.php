<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class WishlistApiController extends Controller
{
    /**
     * Get user's wishlist
     */
    public function index(): JsonResponse
    {
        $wishlistItems = Wishlist::with(['product.category'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $wishlistItems,
                'count' => $wishlistItems->count()
            ]
        ]);
    }

    /**
     * Get wishlist count
     */
    public function count(): JsonResponse
    {
        $count = Wishlist::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'data' => [
                'count' => $count
            ]
        ]);
    }

    /**
     * Check if product is in wishlist
     */
    public function check(Product $product): JsonResponse
    {
        $inWishlist = Wishlist::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->exists();

        return response()->json([
            'success' => true,
            'data' => [
                'in_wishlist' => $inWishlist
            ]
        ]);
    }

    /**
     * Toggle product in wishlist
     */
    public function toggle(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $productId = $request->product_id;
        $userId = Auth::id();

        $wishlistItem = Wishlist::where('user_id', $userId)
            ->where('product_id', $productId)
            ->first();

        if ($wishlistItem) {
            // Remove from wishlist
            $wishlistItem->delete();
            $inWishlist = false;
            $message = 'Produk dihapus dari wishlist';
        } else {
            // Add to wishlist
            Wishlist::create([
                'user_id' => $userId,
                'product_id' => $productId
            ]);
            $inWishlist = true;
            $message = 'Produk ditambahkan ke wishlist';
        }

        $wishlistCount = Wishlist::where('user_id', $userId)->count();

        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => [
                'in_wishlist' => $inWishlist,
                'wishlist_count' => $wishlistCount
            ]
        ]);
    }

    /**
     * Remove product from wishlist
     */
    public function remove(Product $product): JsonResponse
    {
        $deleted = Wishlist::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->delete();

        if ($deleted) {
            $wishlistCount = Wishlist::where('user_id', Auth::id())->count();

            return response()->json([
                'success' => true,
                'message' => 'Produk dihapus dari wishlist',
                'data' => [
                    'wishlist_count' => $wishlistCount
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Produk tidak ditemukan di wishlist'
        ], 404);
    }

    /**
     * Clear entire wishlist
     */
    public function clear(): JsonResponse
    {
        $deleted = Wishlist::where('user_id', Auth::id())->delete();

        return response()->json([
            'success' => true,
            'message' => "Wishlist berhasil dikosongkan ({$deleted} item dihapus)",
            'data' => [
                'wishlist_count' => 0
            ]
        ]);
    }

    /**
     * Move wishlist item to cart
     */
    public function moveToCart(Request $request, Product $product): JsonResponse
    {
        $request->validate([
            'quantity' => 'integer|min:1|max:10',
            'size' => 'nullable|string',
            'color' => 'nullable|string'
        ]);

        // Check if product is in wishlist
        $wishlistItem = Wishlist::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->first();

        if (!$wishlistItem) {
            return response()->json([
                'success' => false,
                'message' => 'Produk tidak ditemukan di wishlist'
            ], 404);
        }

        // Check stock
        $quantity = $request->get('quantity', 1);
        if ($product->stock_quantity < $quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Stok tidak mencukupi'
            ], 400);
        }

        // Add to cart (using existing cart logic)
        $cartController = new CartApiController();
        $cartRequest = new Request([
            'product_id' => $product->id,
            'quantity' => $quantity,
            'size' => $request->size,
            'color' => $request->color
        ]);

        $cartResponse = $cartController->add($cartRequest);
        $cartData = json_decode($cartResponse->getContent(), true);

        if ($cartData['success']) {
            // Remove from wishlist after successful cart addition
            $wishlistItem->delete();
            $wishlistCount = Wishlist::where('user_id', Auth::id())->count();

            return response()->json([
                'success' => true,
                'message' => 'Produk dipindahkan ke keranjang',
                'data' => [
                    'cart' => $cartData['data'],
                    'wishlist_count' => $wishlistCount
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $cartData['message'] ?? 'Gagal menambahkan ke keranjang'
        ], 400);
    }

    /**
     * Get wishlist statistics
     */
    public function statistics(): JsonResponse
    {
        $userId = Auth::id();
        
        $totalItems = Wishlist::where('user_id', $userId)->count();
        
        $totalValue = Wishlist::where('user_id', $userId)
            ->join('products', 'wishlists.product_id', '=', 'products.id')
            ->sum('products.price');

        $categoryCounts = Wishlist::where('user_id', $userId)
            ->join('products', 'wishlists.product_id', '=', 'products.id')
            ->join('categories', 'products.category_id', '=', 'categories.id')
            ->selectRaw('categories.name, COUNT(*) as count')
            ->groupBy('categories.name')
            ->pluck('count', 'categories.name');

        $recentlyAdded = Wishlist::with(['product.category'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'total_items' => $totalItems,
                'total_value' => $totalValue,
                'formatted_total_value' => 'Rp ' . number_format($totalValue, 0, ',', '.'),
                'category_counts' => $categoryCounts,
                'recently_added' => $recentlyAdded
            ]
        ]);
    }
}
