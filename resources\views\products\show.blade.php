@extends('layouts.app')

@section('title', $product->name)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('home') }}" class="text-gray-700 hover:text-gray-900">Beranda</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ route('products.index') }}" class="ml-1 text-gray-700 hover:text-gray-900">Produk</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ route('products.index', ['category' => $product->category->slug]) }}" class="ml-1 text-gray-700 hover:text-gray-900">{{ $product->category->name }}</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500">{{ $product->name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Product Images -->
        <div class="space-y-4">
            <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                @if($product->images && count($product->images) > 0)
                    <img id="main-image" src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-96 object-cover">
                @else
                    <div class="w-full h-96 bg-gray-300 flex items-center justify-center">
                        <span class="text-gray-500">No Image</span>
                    </div>
                @endif
            </div>
            
            @if($product->images && count($product->images) > 1)
                <div class="grid grid-cols-4 gap-2">
                    @foreach($product->images as $image)
                        <button onclick="changeMainImage('{{ $image }}')" class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden hover:opacity-75">
                            <img src="{{ $image }}" alt="{{ $product->name }}" class="w-full h-20 object-cover">
                        </button>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Product Info -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $product->name }}</h1>
                <p class="text-lg text-gray-600 mt-2">{{ $product->category->name }}</p>
            </div>

            <!-- Rating -->
            @if($product->rating > 0)
                <div class="flex items-center space-x-2">
                    <div class="flex items-center">
                        @for($i = 1; $i <= 5; $i++)
                            <svg class="w-5 h-5 {{ $i <= $product->rating ? 'text-yellow-400' : 'text-gray-300' }} fill-current" viewBox="0 0 20 20">
                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                            </svg>
                        @endfor
                    </div>
                    <span class="text-sm text-gray-600">({{ $product->reviews->count() }} ulasan)</span>
                </div>
            @endif

            <!-- Price -->
            <div class="space-y-2">
                @if($product->is_on_sale)
                    <div class="flex items-center space-x-3">
                        <span class="text-3xl font-bold text-gray-900">Rp {{ number_format($product->sale_price, 0, ',', '.') }}</span>
                        <span class="text-xl text-gray-500 line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                        <span class="bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded">
                            {{ round((($product->price - $product->sale_price) / $product->price) * 100) }}% OFF
                        </span>
                    </div>
                @else
                    <span class="text-3xl font-bold text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                @endif
            </div>

            <!-- Short Description -->
            <p class="text-gray-700">{{ $product->short_description }}</p>

            <!-- Add to Cart Form -->
            <form action="{{ route('cart.add') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="product_id" value="{{ $product->id }}">
                
                <!-- Variants -->
                @if($product->variants->count() > 0)
                    <!-- Size Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ukuran</label>
                        <div class="grid grid-cols-4 gap-2">
                            @foreach($product->variants->groupBy('size') as $size => $variants)
                                <label class="relative">
                                    <input type="radio" name="size" value="{{ $size }}" class="sr-only peer" required>
                                    <div class="border border-gray-300 rounded-lg p-3 text-center cursor-pointer peer-checked:border-gray-900 peer-checked:bg-gray-900 peer-checked:text-white hover:border-gray-400">
                                        {{ $size }}
                                    </div>
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <!-- Color Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Warna</label>
                        <div class="flex space-x-2">
                            @foreach($product->variants->groupBy('color') as $color => $variants)
                                <label class="relative">
                                    <input type="radio" name="color" value="{{ $color }}" class="sr-only peer" required>
                                    <div class="w-8 h-8 rounded-full border-2 border-gray-300 cursor-pointer peer-checked:border-gray-900 peer-checked:ring-2 peer-checked:ring-gray-900 peer-checked:ring-offset-2" 
                                         style="background-color: {{ $variants->first()->color_code }}" 
                                         title="{{ $color }}">
                                    </div>
                                </label>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Quantity -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah</label>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="decrementQuantity()" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                            </svg>
                        </button>
                        <input type="number" name="quantity" id="quantity" value="1" min="1" max="{{ $product->stock_quantity }}" class="w-20 text-center border border-gray-300 rounded-md py-2">
                        <button type="button" onclick="incrementQuantity()" class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Stok tersedia: {{ $product->stock_quantity }}</p>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <button type="submit" class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition duration-300">
                        Tambah ke Keranjang
                    </button>
                    
                    @auth
                        <button type="button" onclick="toggleWishlist({{ $product->id }})" class="w-full border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.682l-1.318-1.364a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            Tambah ke Wishlist
                        </button>
                    @endauth
                </div>
            </form>

            <!-- Product Attributes -->
            @if($product->attributes)
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Detail Produk</h3>
                    <dl class="space-y-2">
                        @foreach($product->attributes as $key => $value)
                            <div class="flex">
                                <dt class="w-1/3 text-sm font-medium text-gray-700 capitalize">{{ str_replace('_', ' ', $key) }}:</dt>
                                <dd class="text-sm text-gray-900">{{ $value }}</dd>
                            </div>
                        @endforeach
                    </dl>
                </div>
            @endif
        </div>
    </div>

    <!-- Product Description -->
    <div class="mt-12 border-t border-gray-200 pt-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Deskripsi Produk</h2>
        <div class="prose max-w-none text-gray-700">
            {!! nl2br(e($product->description)) !!}
        </div>
    </div>

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
        <div class="mt-12 border-t border-gray-200 pt-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Produk Terkait</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-md transition duration-300">
                        <a href="{{ route('products.show', $relatedProduct) }}">
                            <div class="aspect-w-1 aspect-h-1 bg-gray-200">
                                @if($relatedProduct->images && count($relatedProduct->images) > 0)
                                    <img src="{{ $relatedProduct->main_image }}" alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover group-hover:scale-105 transition duration-300">
                                @else
                                    <div class="w-full h-48 bg-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500">No Image</span>
                                    </div>
                                @endif
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ $relatedProduct->name }}</h3>
                                <div class="flex items-center justify-between">
                                    <div>
                                        @if($relatedProduct->is_on_sale)
                                            <span class="text-lg font-bold text-gray-900">Rp {{ number_format($relatedProduct->sale_price, 0, ',', '.') }}</span>
                                            <span class="text-sm text-gray-500 line-through ml-2">Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</span>
                                        @else
                                            <span class="text-lg font-bold text-gray-900">Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>

<script>
function changeMainImage(src) {
    document.getElementById('main-image').src = src;
}

function incrementQuantity() {
    const input = document.getElementById('quantity');
    const max = parseInt(input.getAttribute('max'));
    if (parseInt(input.value) < max) {
        input.value = parseInt(input.value) + 1;
    }
}

function decrementQuantity() {
    const input = document.getElementById('quantity');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

function toggleWishlist(productId) {
    fetch(`/wishlist/${productId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button text or show notification
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
