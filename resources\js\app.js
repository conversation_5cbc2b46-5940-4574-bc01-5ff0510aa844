import './bootstrap';
import '../css/app.css';

// Modern E-commerce Web Application Features
class SerasiStyleApp {
    constructor() {
        this.cart = new CartManager();

        // Initialize other managers only if their classes are available
        if (typeof SearchManager !== 'undefined') {
            this.search = new SearchManager();
        }

        if (typeof ProductManager !== 'undefined') {
            this.products = new ProductManager();
        }

        if (typeof WishlistManager !== 'undefined') {
            this.wishlist = new WishlistManager();
        }

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCartCount();
        this.initializeComponents();
    }

    setupEventListeners() {
        // Global event listeners
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeComponents();
        });

        // Cart events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-add-to-cart]')) {
                e.preventDefault();
                this.cart.addToCart(e.target);
            }

            if (e.target.matches('[data-remove-from-cart]')) {
                e.preventDefault();
                this.cart.removeFromCart(e.target.dataset.itemId);
            }

            if (e.target.matches('[data-update-quantity]')) {
                this.cart.updateQuantity(e.target);
            }
        });

        // Search events
        const searchInput = document.querySelector('#search-input');
        if (searchInput && this.search) {
            searchInput.addEventListener('input', (e) => {
                this.search.handleSearch(e.target.value);
            });
        }

        // Product filter events
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-filter]')) {
                this.products.applyFilters();
            }
        });
    }

    initializeComponents() {
        // Initialize tooltips, modals, etc.
        this.initializeTooltips();
        this.initializeModals();
        this.initializeImageGallery();
    }

    initializeTooltips() {
        // Add tooltip functionality
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(tooltip => {
            tooltip.addEventListener('mouseenter', this.showTooltip);
            tooltip.addEventListener('mouseleave', this.hideTooltip);
        });
    }

    initializeModals() {
        // Modal functionality
        const modalTriggers = document.querySelectorAll('[data-modal-target]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.dataset.modalTarget;
                this.openModal(modalId);
            });
        });

        const modalCloses = document.querySelectorAll('[data-modal-close]');
        modalCloses.forEach(close => {
            close.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal(close.closest('.modal'));
            });
        });
    }

    initializeImageGallery() {
        // Product image gallery
        const mainImage = document.querySelector('#main-product-image');
        const thumbnails = document.querySelectorAll('.product-thumbnail');

        thumbnails.forEach(thumb => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                if (mainImage) {
                    mainImage.src = thumb.src;
                    mainImage.alt = thumb.alt;
                }

                // Update active thumbnail
                thumbnails.forEach(t => t.classList.remove('active'));
                thumb.classList.add('active');
            });
        });
    }

    showTooltip(e) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = e.target.dataset.tooltip;
        document.body.appendChild(tooltip);

        const rect = e.target.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    openModal(modalId) {
        const modal = document.querySelector(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            document.body.classList.remove('modal-open');
        }
    }

    updateCartCount() {
        fetch('/api/cart/count')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const cartCount = document.querySelector('#cart-count');
                    if (cartCount) {
                        cartCount.textContent = data.data.count;
                        cartCount.style.display = data.data.count > 0 ? 'inline' : 'none';
                    }
                }
            })
            .catch(error => console.error('Error updating cart count:', error));
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }
}

// Cart Management Class
class CartManager {
    constructor() {
        this.apiUrl = '/api/cart';
    }

    async addToCart(button) {
        const productId = button.dataset.productId;
        const quantity = parseInt(button.dataset.quantity || 1);
        const size = button.dataset.size || null;
        const color = button.dataset.color || null;

        // Get values from form if available
        const form = button.closest('form');
        if (form) {
            const quantityInput = form.querySelector('[name="quantity"]');
            const sizeSelect = form.querySelector('[name="size"]');
            const colorSelect = form.querySelector('[name="color"]');

            if (quantityInput) quantity = parseInt(quantityInput.value);
            if (sizeSelect) size = sizeSelect.value;
            if (colorSelect) color = colorSelect.value;
        }

        try {
            button.disabled = true;
            button.textContent = 'Menambahkan...';

            const response = await fetch(`${this.apiUrl}/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity,
                    size: size,
                    color: color
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateCartUI(data.data);
                app.showNotification(data.message, 'success');
                this.animateCartIcon();
            } else {
                app.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            app.showNotification('Terjadi kesalahan saat menambahkan ke keranjang', 'error');
        } finally {
            button.disabled = false;
            button.textContent = 'Tambah ke Keranjang';
        }
    }

    async removeFromCart(itemId) {
        try {
            const response = await fetch(`${this.apiUrl}/remove/${itemId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                this.updateCartUI(data.data);
                app.showNotification(data.message, 'success');

                // Remove item from DOM
                const itemElement = document.querySelector(`[data-cart-item="${itemId}"]`);
                if (itemElement) {
                    itemElement.remove();
                }
            } else {
                app.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error removing from cart:', error);
            app.showNotification('Terjadi kesalahan saat menghapus dari keranjang', 'error');
        }
    }

    async updateQuantity(input) {
        const itemId = input.dataset.itemId;
        const quantity = parseInt(input.value);

        if (quantity < 1) {
            input.value = 1;
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}/update/${itemId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    quantity: quantity
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateCartUI(data.data);
                this.updateItemTotal(itemId, quantity);
            } else {
                app.showNotification(data.message, 'error');
                // Revert quantity
                input.value = input.dataset.originalValue || 1;
            }
        } catch (error) {
            console.error('Error updating quantity:', error);
            app.showNotification('Terjadi kesalahan saat memperbarui jumlah', 'error');
        }
    }

    updateCartUI(cartData) {
        // Update cart count
        const cartCount = document.querySelector('#cart-count');
        if (cartCount) {
            cartCount.textContent = cartData.summary.total_quantity;
            cartCount.style.display = cartData.summary.total_quantity > 0 ? 'inline' : 'none';
        }

        // Update cart total
        const cartTotal = document.querySelector('#cart-total');
        if (cartTotal) {
            cartTotal.textContent = cartData.summary.formatted_subtotal;
        }

        // Update mini cart if exists
        this.updateMiniCart(cartData.items);
    }

    updateMiniCart(items) {
        const miniCart = document.querySelector('#mini-cart-items');
        if (!miniCart) return;

        if (items.length === 0) {
            miniCart.innerHTML = '<p class="text-gray-500 text-center py-4">Keranjang kosong</p>';
            return;
        }

        miniCart.innerHTML = items.map(item => `
            <div class="flex items-center space-x-3 py-2">
                <img src="${item.product.main_image}" alt="${item.product.name}" class="w-12 h-12 object-cover rounded">
                <div class="flex-1">
                    <h4 class="text-sm font-medium">${item.product.name}</h4>
                    <p class="text-xs text-gray-500">${item.quantity} x ${item.product.formatted_price}</p>
                </div>
                <button data-remove-from-cart data-item-id="${item.id}" class="text-red-500 hover:text-red-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `).join('');
    }

    updateItemTotal(itemId, quantity) {
        const itemElement = document.querySelector(`[data-cart-item="${itemId}"]`);
        if (!itemElement) return;

        const priceElement = itemElement.querySelector('[data-item-price]');
        const totalElement = itemElement.querySelector('[data-item-total]');

        if (priceElement && totalElement) {
            const price = parseFloat(priceElement.dataset.price);
            const total = price * quantity;
            totalElement.textContent = 'Rp ' + total.toLocaleString('id-ID');
        }
    }

    animateCartIcon() {
        const cartIcon = document.querySelector('#cart-icon');
        if (cartIcon) {
            cartIcon.classList.add('animate-bounce');
            setTimeout(() => {
                cartIcon.classList.remove('animate-bounce');
            }, 1000);
        }
    }
}
