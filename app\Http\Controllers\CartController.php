<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CartController extends Controller
{
    public function index()
    {
        $cartItems = $this->getCartItems();
        $total = $cartItems->sum('total_price');
        
        return view('cart.index', compact('cartItems', 'total'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'product_variant_id' => 'nullable|exists:product_variants,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $product = Product::findOrFail($request->product_id);
        $variant = $request->product_variant_id ? ProductVariant::findOrFail($request->product_variant_id) : null;

        // Check stock
        $availableStock = $variant ? $variant->stock_quantity : $product->stock_quantity;
        if ($request->quantity > $availableStock) {
            return back()->with('error', 'Stok tidak mencukupi');
        }

        $price = $variant ? $variant->final_price : $product->current_price;

        // Check if item already exists in cart
        $existingItem = Cart::where('product_id', $product->id)
            ->where('product_variant_id', $variant?->id)
            ->where(function ($query) {
                if (Auth::check()) {
                    $query->where('user_id', Auth::id());
                } else {
                    $query->where('session_id', session()->getId());
                }
            })
            ->first();

        if ($existingItem) {
            $newQuantity = $existingItem->quantity + $request->quantity;
            if ($newQuantity > $availableStock) {
                return back()->with('error', 'Stok tidak mencukupi');
            }
            $existingItem->update(['quantity' => $newQuantity]);
        } else {
            Cart::create([
                'user_id' => Auth::id(),
                'session_id' => Auth::check() ? null : session()->getId(),
                'product_id' => $product->id,
                'product_variant_id' => $variant?->id,
                'quantity' => $request->quantity,
                'price' => $price,
            ]);
        }

        return back()->with('success', 'Produk berhasil ditambahkan ke keranjang');
    }

    public function update(Request $request, Cart $cart)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        // Check if user owns this cart item
        if (!$this->userOwnsCartItem($cart)) {
            abort(403);
        }

        // Check stock
        $availableStock = $cart->productVariant 
            ? $cart->productVariant->stock_quantity 
            : $cart->product->stock_quantity;

        if ($request->quantity > $availableStock) {
            return back()->with('error', 'Stok tidak mencukupi');
        }

        $cart->update(['quantity' => $request->quantity]);

        return back()->with('success', 'Keranjang berhasil diperbarui');
    }

    public function remove(Cart $cart)
    {
        if (!$this->userOwnsCartItem($cart)) {
            abort(403);
        }

        $cart->delete();

        return back()->with('success', 'Produk berhasil dihapus dari keranjang');
    }

    public function clear()
    {
        $this->getCartItems()->each->delete();

        return back()->with('success', 'Keranjang berhasil dikosongkan');
    }

    private function getCartItems()
    {
        $query = Cart::with(['product', 'productVariant']);

        if (Auth::check()) {
            $query->where('user_id', Auth::id());
        } else {
            $query->where('session_id', session()->getId());
        }

        return $query->get();
    }

    private function userOwnsCartItem(Cart $cart)
    {
        if (Auth::check()) {
            return $cart->user_id === Auth::id();
        } else {
            return $cart->session_id === session()->getId();
        }
    }
}
