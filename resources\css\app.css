@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Figtree', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Custom styles for Serasi Style monochrome theme */
.btn-primary {
    @apply bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition duration-300;
}

.btn-secondary {
    @apply bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition duration-300;
}

.btn-outline {
    @apply border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-300;
}

.card {
    @apply bg-white rounded-lg shadow-sm overflow-hidden;
}

.input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent;
}

/* Product card hover effects */
.product-card {
    @apply transition-all duration-300 hover:shadow-lg;
}

.product-card:hover .product-image {
    @apply scale-105;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
}
