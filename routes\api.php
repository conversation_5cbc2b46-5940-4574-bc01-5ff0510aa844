<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProductApiController;
use App\Http\Controllers\Api\CartApiController;
use App\Http\Controllers\Api\WishlistApiController;

// User authentication
Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Product API routes
Route::prefix('products')->group(function () {
    Route::get('/', [ProductApiController::class, 'index']);
    Route::get('/suggestions', [ProductApiController::class, 'suggestions']);
    Route::get('/categories', [ProductApiController::class, 'categories']);
    Route::get('/price-range', [ProductApiController::class, 'priceRange']);
    Route::get('/{product}', [ProductApiController::class, 'show']);
    Route::get('/{product}/related', [ProductApiController::class, 'related']);
    Route::post('/{product}/variant-stock', [ProductApiController::class, 'variantStock']);
});

// Cart API routes
Route::prefix('cart')->group(function () {
    Route::get('/', [CartApiController::class, 'index']);
    Route::get('/count', [CartApiController::class, 'count']);
    Route::post('/add', [CartApiController::class, 'add']);
    Route::put('/update/{itemId}', [CartApiController::class, 'update']);
    Route::delete('/remove/{itemId}', [CartApiController::class, 'remove']);
    Route::delete('/clear', [CartApiController::class, 'clear']);
});

// Wishlist API routes (requires authentication)
Route::middleware('auth')->prefix('wishlist')->group(function () {
    Route::get('/', [WishlistApiController::class, 'index']);
    Route::get('/count', [WishlistApiController::class, 'count']);
    Route::get('/check/{product}', [WishlistApiController::class, 'check']);
    Route::post('/toggle', [WishlistApiController::class, 'toggle']);
    Route::delete('/remove/{product}', [WishlistApiController::class, 'remove']);
    Route::delete('/clear', [WishlistApiController::class, 'clear']);
});
