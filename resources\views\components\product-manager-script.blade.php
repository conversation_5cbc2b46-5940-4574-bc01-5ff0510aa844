// Product Management Class
class ProductManager {
    constructor() {
        this.apiUrl = '/api/products';
        this.currentFilters = {};
        this.setupFilterHandlers();
    }

    setupFilterHandlers() {
        // Category filters
        const categoryFilters = document.querySelectorAll('[data-filter="category"]');
        categoryFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.applyFilters();
            });
        });

        // Price range filters
        const priceInputs = document.querySelectorAll('#min-price, #max-price');
        priceInputs.forEach(input => {
            input.addEventListener('input', this.debounce(() => {
                this.applyFilters();
            }, 500));
        });

        // Sort dropdown
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.applyFilters();
            });
        }
    }

    async applyFilters() {
        const filters = this.getCurrentFilters();
        
        try {
            const params = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key] !== null && filters[key] !== '') {
                    params.append(key, filters[key]);
                }
            });

            const response = await fetch(`${this.apiUrl}?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                this.updateProductGrid(data.data);
            }
        } catch (error) {
            console.error('Error applying filters:', error);
        }
    }

    getCurrentFilters() {
        const filters = {};

        // Search query
        const searchInput = document.querySelector('#search-input');
        if (searchInput && searchInput.value.trim()) {
            filters.search = searchInput.value.trim();
        }

        // Category filter
        const selectedCategory = document.querySelector('[data-filter="category"]:checked');
        if (selectedCategory && selectedCategory.value !== 'all') {
            filters.category = selectedCategory.value;
        }

        // Price range
        const minPrice = document.querySelector('#min-price');
        const maxPrice = document.querySelector('#max-price');
        if (minPrice && minPrice.value) filters.min_price = minPrice.value;
        if (maxPrice && maxPrice.value) filters.max_price = maxPrice.value;

        // Sort option
        const sortSelect = document.querySelector('#sort-select');
        if (sortSelect && sortSelect.value) {
            filters.sort_by = sortSelect.value;
        }

        return filters;
    }

    updateProductGrid(products) {
        const productGrid = document.querySelector('#product-grid');
        if (!productGrid) return;

        if (products.length === 0) {
            productGrid.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="text-gray-400 text-6xl mb-4">🔍</div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Tidak ada produk ditemukan</h3>
                    <p class="text-gray-500 mb-4">Coba ubah filter pencarian Anda</p>
                </div>
            `;
            return;
        }

        productGrid.innerHTML = products.map(product => this.createProductCard(product)).join('');
    }

    createProductCard(product) {
        const salePrice = product.sale_price ? `
            <div class="flex items-center space-x-2">
                <span class="text-lg font-bold text-gray-900">${product.formatted_sale_price}</span>
                <span class="text-sm text-gray-500 line-through">${product.formatted_price}</span>
            </div>
        ` : `
            <span class="text-lg font-bold text-gray-900">${product.formatted_price}</span>
        `;

        return `
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="relative">
                    <a href="/products/${product.id}">
                        <img src="${product.main_image}" alt="${product.name}" class="w-full h-64 object-cover">
                    </a>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        <a href="/products/${product.id}" class="hover:text-gray-700">${product.name}</a>
                    </h3>
                    <div class="flex items-center justify-between">
                        ${salePrice}
                        <button data-add-to-cart data-product-id="${product.id}" 
                                class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 transition-colors text-sm">
                            Tambah ke Keranjang
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Export for use in main app
window.ProductManager = ProductManager;
